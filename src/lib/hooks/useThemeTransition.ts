"use client";

import { useEffect } from 'react';

/**
 * Hook to handle smooth theme transitions and prevent flash of unstyled content
 */
export function useThemeTransition() {
  useEffect(() => {
    // Remove no-transition class after initial load to enable smooth transitions
    const timer = setTimeout(() => {
      document.body.classList.remove('no-transition');
    }, 100);

    // Add no-transition class on page load to prevent flash
    document.body.classList.add('no-transition');

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Add support for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleMotionChange = (e: MediaQueryListEvent) => {
      if (e.matches) {
        document.documentElement.style.setProperty('--transition-duration', '0s');
      } else {
        document.documentElement.style.removeProperty('--transition-duration');
      }
    };

    handleMotionChange(mediaQuery as any);
    mediaQuery.addEventListener('change', handleMotionChange);

    return () => mediaQuery.removeEventListener('change', handleMotionChange);
  }, []);
}

/**
 * ZenUI-style smooth circular transition for theme switching
 * Creates a seamless expanding circle effect from the click point
 */
export function createCircularTransition(
  event: React.MouseEvent,
  callback: () => void,
  duration: number = 800
) {
  if (!('startViewTransition' in document)) {
    callback();
    return;
  }

  const rect = event.currentTarget.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;

  // Calculate the maximum distance to any corner for full coverage
  const endRadius = Math.hypot(
    Math.max(x, window.innerWidth - x),
    Math.max(y, window.innerHeight - y)
  ) * 1.2; // Add 20% buffer for smooth edge coverage

  const transition = (document as any).startViewTransition(callback);

  transition.ready.then(() => {
    // ZenUI-style smooth expanding transition
    document.documentElement.animate(
      {
        clipPath: [
          `circle(0px at ${x}px ${y}px)`,
          `circle(${endRadius}px at ${x}px ${y}px)`
        ],
      },
      {
        duration,
        easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', // ZenUI-like smooth easing
        pseudoElement: '::view-transition-new(root)',
      }
    );
  });
}

/**
 * ZenUI-style contracting transition for light to dark mode
 * Creates a smooth contracting circle effect that matches the expanding transition
 */
export function createContractingTransition(
  event: React.MouseEvent,
  callback: () => void,
  duration: number = 800
) {
  if (!('startViewTransition' in document)) {
    callback();
    return;
  }

  const rect = event.currentTarget.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;

  // Calculate the maximum distance for full coverage
  const endRadius = Math.hypot(
    Math.max(x, window.innerWidth - x),
    Math.max(y, window.innerHeight - y)
  ) * 1.2; // Add 20% buffer for smooth edge coverage

  const transition = (document as any).startViewTransition(callback);

  transition.ready.then(() => {
    // ZenUI-style smooth contracting transition
    // Use the same easing and timing as the expanding transition for consistency
    document.documentElement.animate(
      {
        clipPath: [
          `circle(${endRadius}px at ${x}px ${y}px)`,
          `circle(0px at ${x}px ${y}px)`
        ],
      },
      {
        duration,
        easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', // Same easing as expanding
        pseudoElement: '::view-transition-old(root)',
      }
    );
  });
}
