"use client";

import { useEffect } from 'react';

/**
 * Hook to handle smooth theme transitions and prevent flash of unstyled content
 */
export function useThemeTransition() {
  useEffect(() => {
    // Remove no-transition class after initial load to enable smooth transitions
    const timer = setTimeout(() => {
      document.body.classList.remove('no-transition');
    }, 100);

    // Add no-transition class on page load to prevent flash
    document.body.classList.add('no-transition');

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Add support for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleMotionChange = (e: MediaQueryListEvent) => {
      if (e.matches) {
        document.documentElement.style.setProperty('--transition-duration', '0s');
      } else {
        document.documentElement.style.removeProperty('--transition-duration');
      }
    };

    handleMotionChange(mediaQuery as any);
    mediaQuery.addEventListener('change', handleMotionChange);

    return () => mediaQuery.removeEventListener('change', handleMotionChange);
  }, []);
}

/**
 * Utility function to create a smooth circular transition effect with contracting animation
 */
export function createCircularTransition(
  event: React.MouseEvent,
  callback: () => void,
  duration: number = 600
) {
  if (!('startViewTransition' in document)) {
    callback();
    return;
  }

  const rect = event.currentTarget.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;
  const endRadius = Math.hypot(
    Math.max(x, window.innerWidth - x),
    Math.max(y, window.innerHeight - y)
  );

  const transition = (document as any).startViewTransition(callback);

  transition.ready.then(() => {
    // Standard expanding transition (works well for dark to light)
    const clipPath = [
      `circle(0px at ${x}px ${y}px)`,
      `circle(${endRadius}px at ${x}px ${y}px)`
    ];

    document.documentElement.animate(
      {
        clipPath: clipPath,
      },
      {
        duration,
        easing: 'ease-in-out',
        pseudoElement: '::view-transition-new(root)',
      }
    );
  });
}

/**
 * Enhanced contracting transition specifically for light to dark mode
 */
export function createContractingTransition(
  event: React.MouseEvent,
  callback: () => void,
  duration: number = 500
) {
  if (!('startViewTransition' in document)) {
    callback();
    return;
  }

  const rect = event.currentTarget.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;
  const endRadius = Math.hypot(
    Math.max(x, window.innerWidth - x),
    Math.max(y, window.innerHeight - y)
  );

  const transition = (document as any).startViewTransition(callback);

  transition.ready.then(() => {
    // Contract the old light theme from full screen to center point
    document.documentElement.animate(
      {
        clipPath: [
          `circle(${endRadius}px at ${x}px ${y}px)`,
          `circle(0px at ${x}px ${y}px)`
        ],
      },
      {
        duration,
        easing: 'ease-in',
        pseudoElement: '::view-transition-old(root)',
      }
    );
  });
}
