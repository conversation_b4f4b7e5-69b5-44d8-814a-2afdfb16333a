"use client";

import { useEffect } from 'react';

/**
 * Hook to handle smooth theme transitions and prevent flash of unstyled content
 */
export function useThemeTransition() {
  useEffect(() => {
    // Remove no-transition class after initial load to enable smooth transitions
    const timer = setTimeout(() => {
      document.body.classList.remove('no-transition');
    }, 100);

    // Add no-transition class on page load to prevent flash
    document.body.classList.add('no-transition');

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Add support for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleMotionChange = (e: MediaQueryListEvent) => {
      if (e.matches) {
        document.documentElement.style.setProperty('--transition-duration', '0s');
      } else {
        document.documentElement.style.removeProperty('--transition-duration');
      }
    };

    handleMotionChange(mediaQuery as any);
    mediaQuery.addEventListener('change', handleMotionChange);

    return () => mediaQuery.removeEventListener('change', handleMotionChange);
  }, []);
}

/**
 * Utility function to create a smooth circular transition effect for dark to light mode
 */
export function createCircularTransition(
  event: React.MouseEvent,
  callback: () => void,
  duration: number = 500
) {
  if (!('startViewTransition' in document)) {
    callback();
    return;
  }

  const rect = event.currentTarget.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;
  const endRadius = Math.hypot(
    Math.max(x, window.innerWidth - x),
    Math.max(y, window.innerHeight - y)
  );

  const transition = (document as any).startViewTransition(callback);

  transition.ready.then(() => {
    // Animate both the old (dark) theme contracting and new (light) theme expanding
    // This ensures consistency with the contracting transition

    // Contract the old dark theme
    document.documentElement.animate(
      {
        clipPath: [
          `circle(${endRadius}px at ${x}px ${y}px)`,
          `circle(0px at ${x}px ${y}px)`
        ],
      },
      {
        duration: duration * 0.7,
        easing: 'cubic-bezier(0.4, 0, 0.6, 1)',
        pseudoElement: '::view-transition-old(root)',
      }
    );

    // Expand the new light theme from center point
    setTimeout(() => {
      document.documentElement.animate(
        {
          clipPath: [
            `circle(0px at ${x}px ${y}px)`,
            `circle(${endRadius}px at ${x}px ${y}px)`
          ],
        },
        {
          duration: duration * 0.8,
          easing: 'cubic-bezier(0.2, 0, 0.4, 1)',
          pseudoElement: '::view-transition-new(root)',
        }
      );
    }, duration * 0.15);
  });
}

/**
 * Enhanced contracting transition specifically for light to dark mode
 */
export function createContractingTransition(
  event: React.MouseEvent,
  callback: () => void,
  duration: number = 500
) {
  if (!('startViewTransition' in document)) {
    callback();
    return;
  }

  const rect = event.currentTarget.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;
  const endRadius = Math.hypot(
    Math.max(x, window.innerWidth - x),
    Math.max(y, window.innerHeight - y)
  );

  const transition = (document as any).startViewTransition(callback);

  transition.ready.then(() => {
    // Animate both the old (light) theme contracting and new (dark) theme expanding
    // This creates a smoother, more synchronized transition

    // Contract the old light theme from full screen to center point
    document.documentElement.animate(
      {
        clipPath: [
          `circle(${endRadius}px at ${x}px ${y}px)`,
          `circle(0px at ${x}px ${y}px)`
        ],
      },
      {
        duration: duration * 0.7, // Slightly faster contraction
        easing: 'cubic-bezier(0.4, 0, 0.6, 1)', // Smoother easing
        pseudoElement: '::view-transition-old(root)',
      }
    );

    // Expand the new dark theme from center point to full screen
    // Start this animation slightly after the contraction begins for better effect
    setTimeout(() => {
      document.documentElement.animate(
        {
          clipPath: [
            `circle(0px at ${x}px ${y}px)`,
            `circle(${endRadius}px at ${x}px ${y}px)`
          ],
        },
        {
          duration: duration * 0.8, // Slightly longer expansion
          easing: 'cubic-bezier(0.2, 0, 0.4, 1)', // Smooth ease-out
          pseudoElement: '::view-transition-new(root)',
        }
      );
    }, duration * 0.15); // Start expansion 15% into the transition
  });
}
